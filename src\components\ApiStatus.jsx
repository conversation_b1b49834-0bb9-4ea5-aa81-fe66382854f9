import { useState, useEffect } from 'react';
import api from '../services/api';

const ApiStatus = () => {
  const [status, setStatus] = useState('checking');
  const [error, setError] = useState('');

  useEffect(() => {
    checkApiStatus();
  }, []);

  const checkApiStatus = async () => {
    try {
      setStatus('checking');
      // Try to hit a simple endpoint to check if API is accessible
      // Add timeout to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      await api.get('/api/locations/', { signal: controller.signal });
      clearTimeout(timeoutId);
      setStatus('connected');
      setError('');
    } catch (error) {
      setStatus('disconnected');
      if (error.name === 'AbortError') {
        setError('Connection timeout. Backend may be slow or not responding.');
      } else if (error.code === 'ERR_NETWORK') {
        setError('Cannot connect to backend server. Please ensure the backend is running on http://localhost:8000');
      } else if (error.response?.status === 401) {
        setError('Authentication required. Please login.');
      } else if (error.response?.status === 404) {
        setError('API endpoint not found. Backend may be running but endpoints are different.');
      } else {
        setError(`API Error: ${error.response?.data?.message || error.message}`);
      }
    }
  };

  // Only show when there's an actual problem, not during initial checking
  if (status === 'connected' || (status === 'checking' && !error)) {
    return null; // Don't show anything when connected or initial checking
  }

  return (
    <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-md ${
      status === 'checking' ? 'bg-yellow-100 border-yellow-400' :
      status === 'disconnected' ? 'bg-red-100 border-red-400' :
      'bg-green-100 border-green-400'
    } border`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {status === 'checking' && (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-yellow-600"></div>
          )}
          {status === 'disconnected' && (
            <svg className="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          )}
        </div>
        <div className="ml-3">
          <h3 className={`text-sm font-medium ${
            status === 'checking' ? 'text-yellow-800' :
            status === 'disconnected' ? 'text-red-800' :
            'text-green-800'
          }`}>
            {status === 'checking' && 'Checking API Connection...'}
            {status === 'disconnected' && 'API Connection Failed'}
          </h3>
          {error && (
            <div className={`mt-2 text-sm ${
              status === 'disconnected' ? 'text-red-700' : 'text-yellow-700'
            }`}>
              <p>{error}</p>
            </div>
          )}
          {status === 'disconnected' && (
            <div className="mt-3">
              <button
                onClick={checkApiStatus}
                className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
              >
                Retry Connection
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApiStatus;
