import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
// import { authService } from './services/auth';
import { USER_ROLES } from './utils/constants';

// Layout
import Layout from './components/Layout';
import ProtectedRoute from './utils/ProtectedRoute';
// import ApiStatus from './components/ApiStatus';

// Public Pages
import Landing from './pages/public/Landing';
import Login from './pages/public/Login';
import Signup from './pages/public/Signup';
import Debug from './pages/Debug';

// Buyer Pages
import BuyerDashboard from './pages/buyer/Dashboard';
import Order from './pages/buyer/Order';
import Orders from './pages/buyer/Orders';
import RateOrder from './pages/buyer/RateOrder';

// Seller Pages
import SellerDashboard from './pages/seller/Dashboard';
import Inventory from './pages/seller/Inventory';
import SellerOrders from './pages/seller/Orders';
import GenerateInvoice from './pages/seller/Invoice';

// Admin Pages
import AdminDashboard from './pages/admin/Dashboard';
import AdminOrders from './pages/admin/Orders';
import AdminInvoices from './pages/admin/Invoices';
import AdminUsers from './pages/admin/Users';
import AdminReports from './pages/admin/Reports';
import AdminLocations from './pages/admin/Locations';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userRole, setUserRole] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simple, direct authentication check to prevent infinite loops
    const checkAuth = () => {
      try {
        // Direct localStorage check - most reliable
        const token = localStorage.getItem('token');
        const user = localStorage.getItem('user');

        if (token && user) {
          const userData = JSON.parse(user);
          setIsAuthenticated(true);
          setUserRole(userData.role);
        } else {
          setIsAuthenticated(false);
          setUserRole(null);
        }
      } catch (error) {
        console.error("Auth check error:", error);
        setIsAuthenticated(false);
        setUserRole(null);
      } finally {
        setIsLoading(false);
      }
    };

    // Run immediately without delay
    checkAuth();
  }, []);

  const getDefaultRoute = () => {
    if (!isAuthenticated) return '/';
    switch (userRole) {
      case USER_ROLES.BUYER:
      case 'buyer':
        return '/buyer/dashboard';
      case USER_ROLES.SELLER:
      case 'seller':
        return '/seller/dashboard';
      case USER_ROLES.ADMIN:
      case 'admin':
        return '/admin/dashboard';
      default:
        return '/';
    }
  };

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <div className="App">
        {/* ApiStatus removed to prevent infinite loading */}
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={isAuthenticated ? <Navigate to={getDefaultRoute()} replace /> : <Landing />} />
          <Route path="/login" element={isAuthenticated ? <Navigate to={getDefaultRoute()} replace /> : <Login />} />
          <Route path="/signup" element={isAuthenticated ? <Navigate to={getDefaultRoute()} replace /> : <Signup />} />
          <Route path="/debug" element={<Debug />} />

          {/* Buyer Routes */}
          <Route path="/buyer" element={
            <ProtectedRoute allowedRoles={[USER_ROLES.BUYER, 'buyer']}>
              <Layout />
            </ProtectedRoute>
          }>
            <Route path="dashboard" element={<BuyerDashboard />} />
            <Route path="order" element={<Order />} />
            <Route path="orders" element={<Orders />} />
            <Route path="rate" element={<RateOrder />} />
          </Route>

          {/* Seller Routes */}
          <Route path="/seller" element={
            <ProtectedRoute allowedRoles={[USER_ROLES.SELLER, 'seller']}>
              <Layout />
            </ProtectedRoute>
          }>
            <Route path="dashboard" element={<SellerDashboard />} />
            <Route path="inventory" element={<Inventory />} />
            <Route path="orders" element={<SellerOrders />} />
            <Route path="invoice" element={<GenerateInvoice />} />
          </Route>

          {/* Admin Routes */}
          <Route path="/admin" element={
            <ProtectedRoute allowedRoles={[USER_ROLES.ADMIN, 'admin']}>
              <Layout />
            </ProtectedRoute>
          }>
            <Route path="dashboard" element={<AdminDashboard />} />
            <Route path="orders" element={<AdminOrders />} />
            <Route path="invoices" element={<AdminInvoices />} />
            <Route path="users" element={<AdminUsers />} />
            <Route path="locations" element={<AdminLocations />} />
            <Route path="reports" element={<AdminReports />} />
          </Route>

          {/* Unauthorized Page */}
          <Route path="/unauthorized" element={
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">Unauthorized</h1>
                <p className="text-gray-600 mb-8">You don't have permission to access this page.</p>
                <button
                  onClick={() => window.history.back()}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
                >
                  Go Back
                </button>
              </div>
            </div>
          } />

          {/* Catch-all Route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;