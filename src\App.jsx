import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { authService } from './services/auth';
import { USER_ROLES } from './utils/constants';

// Layout
import Layout from './components/Layout';
import ProtectedRoute from './utils/ProtectedRoute';

// Public Pages
import Landing from './pages/public/Landing';
import Login from './pages/public/Login';
import Signup from './pages/public/Signup';

// Buyer Pages
import BuyerDashboard from './pages/buyer/Dashboard';
import Order from './pages/buyer/Order';
import Orders from './pages/buyer/Orders';
import RateOrder from './pages/buyer/RateOrder';
// import ProfilePage from './pages/buyer/profilePage';

// Seller Pages
import SellerDashboard from './pages/seller/Dashboard';
import Inventory from './pages/seller/Inventory';

// Admin Pages
import AdminDashboard from './pages/admin/Dashboard';

function App() {
  let isAuthenticated = false;
  let userRole = null;

  try {
    isAuthenticated = authService.isAuthenticated();
    userRole = authService.getUserRole();
  } catch (error) {
    console.error("AuthService error:", error);
    isAuthenticated = false;
    userRole = null;
  }

  const getDefaultRoute = () => {
    if (!isAuthenticated) return '/';
    switch (userRole) {
      case USER_ROLES.BUYER:
        return '/buyer/dashboard';
      case USER_ROLES.SELLER:
        return '/seller/dashboard';
      case USER_ROLES.ADMIN:
        return '/admin/dashboard';
      default:
        return '/';
    }
  };

  return (
    <Router>
      <div className="App">
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={isAuthenticated ? <Navigate to={getDefaultRoute()} replace /> : <Landing />} />
          <Route path="/login" element={isAuthenticated ? <Navigate to={getDefaultRoute()} replace /> : <Login />} />
          <Route path="/signup" element={isAuthenticated ? <Navigate to={getDefaultRoute()} replace /> : <Signup />} />

          {/* Buyer Routes */}
          <Route path="/buyer" element={
            <ProtectedRoute allowedRoles={[USER_ROLES.BUYER]}>
              <Layout />
            </ProtectedRoute>
          }>
            <Route path="dashboard" element={<BuyerDashboard />} />
            <Route path="order" element={<Order />} />
            <Route path="orders" element={<Orders />} />
            <Route path="rate" element={<RateOrder />} />
            {/* <Route path="profile" element={<ProfilePage />} /> */}
          </Route>

          {/* Seller Routes */}
          <Route path="/seller" element={
            <ProtectedRoute allowedRoles={[USER_ROLES.SELLER]}>
              <Layout />
            </ProtectedRoute>
          }>
            <Route path="dashboard" element={<SellerDashboard />} />
            <Route path="inventory" element={<Inventory />} />
            <Route path="orders" element={<div className="p-8 text-center"><h1 className="text-2xl">Seller Orders - Coming Soon</h1></div>} />
            <Route path="invoice" element={<div className="p-8 text-center"><h1 className="text-2xl">Generate Invoice - Coming Soon</h1></div>} />
          </Route>

          {/* Admin Routes */}
          <Route path="/admin" element={
            <ProtectedRoute allowedRoles={[USER_ROLES.ADMIN]}>
              <Layout />
            </ProtectedRoute>
          }>
            <Route path="dashboard" element={<AdminDashboard />} />
            <Route path="orders" element={<div className="p-8 text-center"><h1 className="text-2xl">Admin Orders - Coming Soon</h1></div>} />
            <Route path="invoices" element={<div className="p-8 text-center"><h1 className="text-2xl">Invoices - Coming Soon</h1></div>} />
            <Route path="users" element={<div className="p-8 text-center"><h1 className="text-2xl">Users - Coming Soon</h1></div>} />
            <Route path="reports" element={<div className="p-8 text-center"><h1 className="text-2xl">Reports - Coming Soon</h1></div>} />
          </Route>

          {/* Unauthorized Page */}
          <Route path="/unauthorized" element={
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">Unauthorized</h1>
                <p className="text-gray-600 mb-8">You don't have permission to access this page.</p>
                <button
                  onClick={() => window.history.back()}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
                >
                  Go Back
                </button>
              </div>
            </div>
          } />

          {/* Catch-all Route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;









// import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
// import { authService } from './services/auth';
// import { USER_ROLES } from './utils/constants';

// // Layout
// import Layout from './components/Layout';
// import ProtectedRoute from './utils/ProtectedRoute';

// // Public Pages
// import Landing from './pages/public/Landing';
// import Login from './pages/public/Login';
// import Signup from './pages/public/Signup';

// // Buyer Pages
// import BuyerDashboard from './pages/buyer/Dashboard';
// import Order from './pages/buyer/Order';
// import Orders from './pages/buyer/Orders';
// import RateOrder from './pages/buyer/RateOrder';

// // Seller Pages
// import SellerDashboard from './pages/seller/Dashboard';
// import Inventory from './pages/seller/Inventory';

// // Admin Pages
// import AdminDashboard from './pages/admin/Dashboard';

// function App() {
//   const isAuthenticated = authService.isAuthenticated();
//   const userRole = authService.getUserRole();

//   // Redirect authenticated users to their dashboard
//   const getDefaultRoute = () => {
//     if (!isAuthenticated) return '/';

//     switch (userRole) {
//       case 'Buyer':
//         return '/buyer/dashboard';
//       case 'Seller':
//         return '/seller/dashboard';
//       case 'Admin':
//         return '/admin/dashboard';
//       default:
//         return '/';
//     }
//   };

//   return (
//     <Router>
//       <div className="App">
//         <Routes>
//           {/* Public Routes */}
//           <Route path="/" element={isAuthenticated ? <Navigate to={getDefaultRoute()} replace /> : <Landing />} />
//           <Route path="/login" element={isAuthenticated ? <Navigate to={getDefaultRoute()} replace /> : <Login />} />
//           <Route path="/signup" element={isAuthenticated ? <Navigate to={getDefaultRoute()} replace /> : <Signup />} />

//           {/* Protected Routes with Layout */}
//           <Route path="/buyer" element={
//             <ProtectedRoute allowedRoles={[USER_ROLES.BUYER]}>
//               <Layout />
//             </ProtectedRoute>
//           }>
//             <Route path="dashboard" element={<BuyerDashboard />} />
//             <Route path="order" element={<Order />} />
//             <Route path="orders" element={<Orders />} />
//             <Route path="rate" element={<RateOrder />} />
//           </Route>

//           {/* Seller Routes */}
//           <Route path="/seller" element={
//             <ProtectedRoute allowedRoles={[USER_ROLES.SELLER]}>
//               <Layout />
//             </ProtectedRoute>
//           }>
//             <Route path="dashboard" element={<SellerDashboard />} />
//             <Route path="inventory" element={<Inventory />} />
//             <Route path="orders" element={<div className="p-8 text-center"><h1 className="text-2xl">Seller Orders - Coming Soon</h1></div>} />
//             <Route path="invoice" element={<div className="p-8 text-center"><h1 className="text-2xl">Generate Invoice - Coming Soon</h1></div>} />
//           </Route>

//           {/* Admin Routes */}
//           <Route path="/admin" element={
//             <ProtectedRoute allowedRoles={[USER_ROLES.ADMIN]}>
//               <Layout />
//             </ProtectedRoute>
//           }>
//             <Route path="dashboard" element={<AdminDashboard />} />
//             <Route path="orders" element={<div className="p-8 text-center"><h1 className="text-2xl">Admin Orders - Coming Soon</h1></div>} />
//             <Route path="invoices" element={<div className="p-8 text-center"><h1 className="text-2xl">Invoices - Coming Soon</h1></div>} />
//             <Route path="users" element={<div className="p-8 text-center"><h1 className="text-2xl">Users - Coming Soon</h1></div>} />
//             <Route path="reports" element={<div className="p-8 text-center"><h1 className="text-2xl">Reports - Coming Soon</h1></div>} />
//           </Route>

//           {/* Unauthorized Route */}
//           <Route path="/unauthorized" element={
//             <div className="min-h-screen flex items-center justify-center bg-gray-50">
//               <div className="text-center">
//                 <h1 className="text-4xl font-bold text-gray-900 mb-4">Unauthorized</h1>
//                 <p className="text-gray-600 mb-8">You don't have permission to access this page.</p>
//                 <button
//                   onClick={() => window.history.back()}
//                   className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
//                 >
//                   Go Back
//                 </button>
//               </div>
//             </div>
//           } />

//           {/* Catch all route */}
//           <Route path="*" element={<Navigate to="/" replace />} />
//         </Routes>
//       </div>
//     </Router>
//   );
// }

// export default App;


