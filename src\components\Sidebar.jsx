import { Link, useLocation } from 'react-router-dom';
import { USER_ROLES } from '../utils/constants';

const Sidebar = ({ user }) => {
  const location = useLocation();

  const getNavigationItems = () => {
    switch (user?.role) {
      case USER_ROLES.BUYER:
        return [
          { name: 'Dashboard', href: '/buyer/dashboard', icon: '📊' },
          { name: 'Order Gas', href: '/buyer/order', icon: '🛒' },
          { name: 'My Orders', href: '/buyer/orders', icon: '📋' },
          { name: 'Rate Order', href: '/buyer/rate', icon: '⭐' },
           { name: 'My Profile', href: '/profile', icon: '👤' },
          
        ];
      case USER_ROLES.SELLER:
        return [
          { name: 'Dashboard', href: '/seller/dashboard', icon: '📊' },
          { name: 'Inventory', href: '/seller/inventory', icon: '📦' },
          { name: 'Orders', href: '/seller/orders', icon: '📋' },
          { name: 'Generate Invoice', href: '/seller/invoice', icon: '📄' },
        ];
      case USER_ROLES.ADMIN:
        return [
          { name: 'Dashboard', href: '/admin/dashboard', icon: '📊' },
          { name: 'Orders', href: '/admin/orders', icon: '📋' },
          { name: 'Invoices', href: '/admin/invoices', icon: '📄' },
          { name: 'Users', href: '/admin/users', icon: '👥' },
          { name: 'Reports', href: '/admin/reports', icon: '📈' },
        ];
      default:
        return [];
    }
  };

  const navigationItems = getNavigationItems();

  return (
    <div className="bg-gray-800 text-white w-64 space-y-6 py-7 px-2 absolute inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition duration-200 ease-in-out">
      <div className="text-center">
        <h2 className="text-2xl font-bold">GasGo</h2>
        <p className="text-gray-400 text-sm mt-2">
          {user?.role?.charAt(0).toUpperCase() + user?.role?.slice(1)} Panel
        </p>
      </div>
      
      <nav className="space-y-2">
        {navigationItems.map((item) => (
          <Link
            key={item.name}
            to={item.href}
            className={`flex items-center space-x-3 py-2 px-4 rounded transition duration-200 ${
              location.pathname === item.href
                ? 'bg-gray-700 text-white'
                : 'text-gray-300 hover:bg-gray-700 hover:text-white'
            }`}
          >
            <span className="text-xl">{item.icon}</span>
            <span>{item.name}</span>
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;
