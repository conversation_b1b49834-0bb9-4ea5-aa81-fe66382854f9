import api from './api';

// Health check service
export const healthService = {
  checkHealth: async () => {
    const response = await api.get('/api/locations/');
    return response.data;
  }
};

// Location endpoints
export const locationService = {
  getLocations: async () => {
    const response = await api.get('/api/locations/');
    return response.data;
  },

  createLocation: async (locationData) => {
    const response = await api.post('/api/locations/create/', locationData);
    return response.data;
  },

  updateLocation: async (id, locationData) => {
    const response = await api.put(`/api/locations/${id}/`, locationData);
    return response.data;
  },

  deleteLocation: async (id) => {
    const response = await api.delete(`/api/locations/${id}/`);
    return response.data;
  }
};

// Buyer endpoints
export const buyerService = {
  getProducts: async () => {
    console.log('🔍 Buyer: Making API call to /api/invetory/all/');
    const response = await api.get('/api/invetory/all/');
    console.log('🔍 Buyer: Raw API response:', response);
    console.log('🔍 Buyer: Response data:', response.data);
    return response.data;
  },

  createOrder: async (orderData) => {
    const response = await api.post('/api/booking/orders/create/', orderData);
    return response.data;
  },

  getOrders: async () => {
    const response = await api.get('/api/booking/orders/');
    return response.data;
  },

  getOrder: async (id) => {
    const response = await api.get(`/api/booking/orders/${id}/`);
    return response.data;
  },

  createRating: async (ratingData) => {
    // Note: Ratings endpoint may not be available in current API
    const response = await api.post('/api/booking/ratings/create/', ratingData);
    return response.data;
  },

  getRatings: async () => {
    // Note: Ratings endpoint may not be available in current API
    const response = await api.get('/api/booking/ratings/');
    return response.data;
  }
};

// Seller endpoints
export const sellerService = {
  getDashboard: async () => {
    const response = await api.get('/api/invetory/all/');
    return response.data;
  },

  getStock: async () => {
    const response = await api.get('/api/invetory/all/');
    return response.data;
  },

  createStock: async (stockData) => {
    const response = await api.post('/api/invetory/create/', stockData);
    return response.data;
  },

  updateStock: async (id, stockData) => {
    const response = await api.put(`/api/invetory/${id}/update/`, stockData);
    return response.data;
  },

  deleteStock: async (id) => {
    const response = await api.delete(`/api/invetory/${id}/delete/`);
    return response.data;
  },

  getOrders: async () => {
    const response = await api.get('/api/booking/orders/seller/');
    return response.data;
  },

  updateOrderStatus: async (orderId, statusData) => {
    const response = await api.put(`/api/booking/orders/${orderId}/update/`, statusData);
    return response.data;
  },

  approveOrder: async (orderId) => {
    const response = await api.post(`/api/booking/orders/${orderId}/approve/`);
    return response.data;
  },

  rejectOrder: async (orderId) => {
    const response = await api.post(`/api/booking/orders/${orderId}/reject/`);
    return response.data;
  },

  markOrderDelivered: async (orderId) => {
    const response = await api.post(`/api/booking/orders/${orderId}/mark-delivered/`);
    return response.data;
  },

  createInvoice: async (invoiceData) => {
    const response = await api.post('/api/invoices/create/', invoiceData);
    return response.data;
  },

  getInvoices: async () => {
    const response = await api.get('/api/invoices/');
    return response.data;
  }
};

// Admin endpoints
export const adminService = {
  getOrders: async () => {
    const response = await api.get('/api/booking/orders/');
    return response.data;
  },

  getOrder: async (orderId) => {
    const response = await api.get(`/api/booking/orders/${orderId}/`);
    return response.data;
  },

  updateOrderStatus: async (orderId, statusData) => {
    const response = await api.put(`/api/booking/orders/${orderId}/update/`, statusData);
    return response.data;
  },

  approveOrder: async (orderId) => {
    const response = await api.post(`/api/booking/orders/${orderId}/approve/`);
    return response.data;
  },

  rejectOrder: async (orderId) => {
    const response = await api.post(`/api/booking/orders/${orderId}/reject/`);
    return response.data;
  },

  markOrderDelivered: async (orderId) => {
    const response = await api.post(`/api/booking/orders/${orderId}/mark-delivered/`);
    return response.data;
  },

  deleteOrder: async (orderId) => {
    const response = await api.delete(`/api/booking/orders/${orderId}/delete/`);
    return response.data;
  },

  getInvoices: async () => {
    const response = await api.get('/api/invoices/');
    return response.data;
  },

  updateInvoiceStatus: async (invoiceId, statusData) => {
    const response = await api.put(`/api/invoices/update/${invoiceId}/`, statusData);
    return response.data;
  },

  getUsers: async () => {
    console.log('🔍 Admin Service: Making API call to /api/accounts/admin/users/all/');
    const response = await api.get('/api/accounts/admin/users/all/');
    console.log('🔍 Admin Service: Raw API response:', response);
    console.log('🔍 Admin Service: Response data:', response.data);
    return response.data;
  },

  updateUserRole: async (userId, roleData) => {
    const response = await api.put(`/api/accounts/users/${userId}/update-role/`, roleData);
    return response.data;
  },

  // Location management for admin
  getLocations: async () => {
    console.log('🔍 Admin Service: Making API call to /api/locations/');
    const response = await api.get('/api/locations/');
    console.log('🔍 Admin Service: Raw API response:', response);
    console.log('🔍 Admin Service: Response data:', response.data);
    return response.data;
  },

  createLocation: async (locationData) => {
    const response = await api.post('/api/locations/create/', locationData);
    return response.data;
  },

  updateLocation: async (id, locationData) => {
    const response = await api.put(`/api/locations/${id}/`, locationData);
    return response.data;
  },

  deleteLocation: async (id) => {
    const response = await api.delete(`/api/locations/${id}/`);
    return response.data;
  },

  // Inventory management for admin
  getInventory: async () => {
    const response = await api.get('/api/invetory/all/');
    return response.data;
  },

  createInventory: async (inventoryData) => {
    const response = await api.post('/api/invetory/create/', inventoryData);
    return response.data;
  },

  updateInventory: async (id, inventoryData) => {
    const response = await api.put(`/api/invetory/${id}/update/`, inventoryData);
    return response.data;
  },

  deleteInventory: async (id) => {
    const response = await api.delete(`/api/invetory/${id}/delete/`);
    return response.data;
  }
};

// Invoice endpoints
export const invoiceService = {
  getInvoices: async () => {
    const response = await api.get('/api/invoices/');
    return response.data;
  },

  getInvoice: async (id) => {
    const response = await api.get(`/api/invoices/${id}/`);
    return response.data;
  },

  createInvoice: async (invoiceData) => {
    const response = await api.post('/api/invoices/create/', invoiceData);
    return response.data;
  },

  updateInvoice: async (id, invoiceData) => {
    const response = await api.put(`/api/invoices/update/${id}/`, invoiceData);
    return response.data;
  },

  deleteInvoice: async (id) => {
    const response = await api.delete(`/api/invoices/delete/${id}/`);
    return response.data;
  }
};
