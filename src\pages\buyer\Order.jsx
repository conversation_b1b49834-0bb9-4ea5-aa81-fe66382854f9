import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { buyerService } from '../../services/endpoints';
import { GAS_TYPES } from '../../utils/constants';
import ProductCard from '../../components/ProductCard';
import api from '../../services/api';

const Order = () => {
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [debugInfo, setDebugInfo] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('Fetching products...');
      const response = await buyerService.getProducts();
      console.log('API Response:', response);

      // Handle different response structures
      let productsData = [];
      if (response?.data) {
        productsData = response.data;
      } else if (Array.isArray(response)) {
        productsData = response;
      } else if (response?.results) {
        productsData = response.results;
      } else {
        productsData = response || [];
      }

      console.log('Processed products data:', productsData);

      // Ensure we have an array
      const finalProducts = Array.isArray(productsData) ? productsData : [];
      setProducts(finalProducts);

      if (finalProducts.length === 0) {
        setError('No products available. Please check if sellers have added inventory.');
      }

    } catch (error) {
      console.error('Error fetching products:', error);

      // Provide more detailed error messages
      if (error.response?.status === 401) {
        setError('Authentication required. Please login again.');
      } else if (error.response?.status === 403) {
        setError('Access denied. You may not have permission to view products.');
      } else if (error.response?.status === 404) {
        setError('Products endpoint not found. Please contact support.');
      } else if (error.code === 'ERR_NETWORK') {
        setError('Network error. Please check your connection and ensure the backend is running.');
      } else {
        setError(`Failed to fetch products: ${error.response?.data?.message || error.message || 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const testEndpoints = async () => {
    setDebugInfo('Testing authentication and endpoints...\n');

    // Check authentication first
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    setDebugInfo(prev => prev + `Auth Token: ${token ? 'Present' : 'Missing'}\n`);
    setDebugInfo(prev => prev + `User Data: ${user ? 'Present' : 'Missing'}\n`);
    if (user) {
      try {
        const userData = JSON.parse(user);
        setDebugInfo(prev => prev + `User Role: ${userData.role || 'Unknown'}\n`);
      } catch (e) {
        setDebugInfo(prev => prev + `User Data: Invalid JSON\n`);
      }
    }
    setDebugInfo(prev => prev + '\nTesting endpoints...\n');

    const endpoints = [
      '/api/invetory/all/',
      '/api/inventory/all/',
      '/api/invetory/',
      '/api/inventory/',
      '/api/products/',
      '/api/products/all/'
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`Testing endpoint: ${endpoint}`);
        const response = await api.get(endpoint);
        console.log(`✅ ${endpoint} - Success:`, response.data);
        setDebugInfo(prev => prev + `\n✅ ${endpoint} - Success (${response.data?.length || 'unknown'} items)`);
      } catch (error) {
        console.log(`❌ ${endpoint} - Error:`, error.response?.status, error.response?.data);
        setDebugInfo(prev => prev + `\n❌ ${endpoint} - Error: ${error.response?.status || error.message}`);
      }
    }
  };

  const handleProductSelect = (product) => {
    setSelectedProduct(product);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!selectedProduct) {
      setError('Please select a product');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const orderData = {
        inventory_id: selectedProduct.id,
        quantity: quantity,
        delivery_address: deliveryAddress,
        contact_phone: '0788000000', // This should come from user profile
        total_price: (selectedProduct.unit_price || selectedProduct.price) * quantity
      };

      await buyerService.createOrder(orderData);
      navigate('/buyer/orders', { 
        state: { message: 'Order placed successfully!' }
      });
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to place order');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Order Gas</h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="font-medium">Error Loading Products</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={fetchProducts}
                className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
              >
                Retry
              </button>
              <button
                onClick={testEndpoints}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
              >
                Debug API
              </button>
            </div>
          </div>
        </div>
      )}

      {debugInfo && (
        <div className="bg-gray-100 border border-gray-300 text-gray-700 px-4 py-3 rounded">
          <p className="font-medium">API Debug Information:</p>
          <pre className="text-xs mt-2 whitespace-pre-wrap">{debugInfo}</pre>
          <button
            onClick={() => setDebugInfo('')}
            className="mt-2 text-gray-500 hover:text-gray-700 text-sm underline"
          >
            Clear Debug Info
          </button>
        </div>
      )}

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Product Selection */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Select Gas Product
          </h2>
          
          {products.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-6xl mb-4">📦</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Available</h3>
              <p className="text-gray-500 mb-4">
                There are currently no gas products available for order.
              </p>
              <p className="text-sm text-gray-400">
                This could mean:
              </p>
              <ul className="text-sm text-gray-400 mt-2 space-y-1">
                <li>• Sellers haven't added inventory yet</li>
                <li>• All products are currently out of stock</li>
                <li>• There's a connection issue with the backend</li>
              </ul>
              <button
                onClick={fetchProducts}
                className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
              >
                Refresh Products
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  isSelected={selectedProduct?.id === product.id}
                  onSelect={() => handleProductSelect(product)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Order Form */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Order Details
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {selectedProduct && (
              <div className="bg-gray-50 p-4 rounded-md">
                <h3 className="font-medium text-gray-900">Selected Product</h3>
                <p className="text-sm text-gray-600">{selectedProduct.brand} - {selectedProduct.weight_kg}kg</p>
                <p className="text-sm text-gray-600">Price: RWF {parseFloat(selectedProduct.unit_price || 0).toLocaleString()}</p>
                <p className="text-sm text-gray-600">Available: {selectedProduct.quantity || 0}</p>
              </div>
            )}
            
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                Quantity
              </label>
              <input
                type="number"
                id="quantity"
                min="1"
                max={selectedProduct?.quantity || 1}
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                required
              />
            </div>
            
            <div>
              <label htmlFor="deliveryAddress" className="block text-sm font-medium text-gray-700">
                Delivery Address
              </label>
              <textarea
                id="deliveryAddress"
                rows={3}
                value={deliveryAddress}
                onChange={(e) => setDeliveryAddress(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter your delivery address"
                required
              />
            </div>
            
            {selectedProduct && (
              <div className="bg-blue-50 p-4 rounded-md">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-900">Total Amount:</span>
                  <span className="text-xl font-bold text-blue-600">
                    RWF {((selectedProduct.unit_price || 0) * quantity).toLocaleString()}
                  </span>
                </div>
              </div>
            )}
            
            <button
              type="submit"
              disabled={!selectedProduct || submitting}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium"
            >
              {submitting ? 'Placing Order...' : 'Place Order'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Order;
