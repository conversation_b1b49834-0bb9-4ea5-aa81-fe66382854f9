import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { buyerService, sellerService } from '../../services/endpoints';
import { GAS_TYPES } from '../../utils/constants';
import ProductCard from '../../components/ProductCard';

const Order = () => {
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🛒 Buyer: Starting to fetch products...');
      console.log('🛒 Buyer: Trying buyerService.getProducts()...');
      const response = await buyerService.getProducts();
      console.log('🛒 Buyer: Got response from buyerService:', response);

      console.log('🛒 Buyer: Now trying sellerService.getStock() for comparison...');
      const sellerResponse = await sellerService.getStock();
      console.log('🛒 Buyer: Got response from sellerService:', sellerResponse);

      // Use the same pattern as seller inventory
      let productsData = response.data || response;
      console.log('🛒 Buyer: Processed products data from buyer service:', productsData);
      console.log('🛒 Buyer: Is array?', Array.isArray(productsData));
      console.log('🛒 Buyer: Length:', productsData?.length);

      // If buyer service returns empty, try seller service
      if (!productsData || !Array.isArray(productsData) || productsData.length === 0) {
        console.log('🛒 Buyer: Buyer service returned empty, using seller service data...');
        productsData = sellerResponse.data || sellerResponse;
        console.log('🛒 Buyer: Using seller data:', productsData);
      }

      setProducts(Array.isArray(productsData) ? productsData : []);
      console.log('🛒 Buyer: Set products state with', productsData?.length, 'items');

    } catch (error) {
      console.error('🛒 Buyer: Error fetching products:', error);
      console.error('🛒 Buyer: Error response:', error.response);
      console.error('🛒 Buyer: Error status:', error.response?.status);
      console.error('🛒 Buyer: Error data:', error.response?.data);
      setError('Failed to fetch products. Please try again.');
    } finally {
      setLoading(false);
    }
  };



  const handleProductSelect = (product) => {
    setSelectedProduct(product);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!selectedProduct) {
      setError('Please select a product');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      const orderData = {
        inventory_id: selectedProduct.id,
        quantity: quantity,
        delivery_address: deliveryAddress,
        contact_phone: '0788000000', // This should come from user profile
        total_price: (selectedProduct.unit_price || selectedProduct.price) * quantity
      };

      await buyerService.createOrder(orderData);
      navigate('/buyer/orders', { 
        state: { message: 'Order placed successfully!' }
      });
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to place order');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Order Gas</h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <div className="flex justify-between items-center">
            <span>{error}</span>
            <button
              onClick={fetchProducts}
              className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Product Selection */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Select Gas Product
          </h2>
          
          {products.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-6xl mb-4">📦</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Available</h3>
              <p className="text-gray-500 mb-4">
                No gas products are currently available for order.
              </p>
              <button
                onClick={fetchProducts}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
              >
                Refresh Products
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  product={product}
                  isSelected={selectedProduct?.id === product.id}
                  onSelect={() => handleProductSelect(product)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Order Form */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Order Details
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {selectedProduct && (
              <div className="bg-gray-50 p-4 rounded-md">
                <h3 className="font-medium text-gray-900">Selected Product</h3>
                <p className="text-sm text-gray-600">{selectedProduct.brand} - {selectedProduct.weight_kg}kg</p>
                <p className="text-sm text-gray-600">Price: RWF {parseFloat(selectedProduct.unit_price || 0).toLocaleString()}</p>
                <p className="text-sm text-gray-600">Available: {selectedProduct.quantity || 0}</p>
              </div>
            )}
            
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                Quantity
              </label>
              <input
                type="number"
                id="quantity"
                min="1"
                max={selectedProduct?.quantity || 1}
                value={quantity}
                onChange={(e) => setQuantity(parseInt(e.target.value))}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                required
              />
            </div>
            
            <div>
              <label htmlFor="deliveryAddress" className="block text-sm font-medium text-gray-700">
                Delivery Address
              </label>
              <textarea
                id="deliveryAddress"
                rows={3}
                value={deliveryAddress}
                onChange={(e) => setDeliveryAddress(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter your delivery address"
                required
              />
            </div>
            
            {selectedProduct && (
              <div className="bg-blue-50 p-4 rounded-md">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-900">Total Amount:</span>
                  <span className="text-xl font-bold text-blue-600">
                    RWF {((selectedProduct.unit_price || 0) * quantity).toLocaleString()}
                  </span>
                </div>
              </div>
            )}
            
            <button
              type="submit"
              disabled={!selectedProduct || submitting}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium"
            >
              {submitting ? 'Placing Order...' : 'Place Order'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Order;
